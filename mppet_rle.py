"""
MPPET-RLE: Multi-Person Pose Estimation and Tracking with Residual Log-likelihood Estimation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from easydict import EasyDict

from RLE_pose import RegressFlow
from detection.detector import FasterRC<PERSON><PERSON>etector
from tracking.tracker import MP<PERSON><PERSON>Tracker
from builder import SP<PERSON><PERSON>


@SPPE.register_module
class MPPET_RLE(nn.Module):
    """
    Multi-Person Pose Estimation and Tracking with RLE uncertainty.
    
    This model combines:
    1. Person detection (Faster R-CNN)
    2. RLE-based pose estimation for each detected person
    3. Distribution-aware tracking using pose uncertainties
    """
    
    def __init__(self, 
                 # Pose estimation config
                 pose_config=None,
                 # Detection config
                 detection_config=None,
                 # Tracking config
                 tracking_config=None,
                 # Training config
                 joint_training=True,
                 pose_loss_weight=1.0,
                 detection_loss_weight=1.0):
        """
        Initialize MPPET-RLE model.
        
        Args:
            pose_config: Configuration for RLE pose estimation
            detection_config: Configuration for person detection
            tracking_config: Configuration for tracking
            joint_training: Whether to train detection and pose jointly
            pose_loss_weight: Weight for pose estimation loss
            detection_loss_weight: Weight for detection loss
        """
        super(MPPET_RLE, self).__init__()
        
        self.joint_training = joint_training
        self.pose_loss_weight = pose_loss_weight
        self.detection_loss_weight = detection_loss_weight
        
        # Default configurations
        if pose_config is None:
            pose_config = self._get_default_pose_config()
        if detection_config is None:
            detection_config = self._get_default_detection_config()
        if tracking_config is None:
            tracking_config = self._get_default_tracking_config()
        
        # Person detector
        self.detector = FasterRCNNDetector(**detection_config)
        
        # RLE pose estimator
        self.pose_estimator = RegressFlow(**pose_config)
        
        # Tracker (not used during training, only inference)
        self.tracker = MPPETTracker(**tracking_config)
        
        # Crop and resize for pose estimation
        self.crop_size = (192, 256)  # (width, height)
        
    def _get_default_pose_config(self):
        """Get default pose estimation configuration."""
        return {
            'NUM_LAYERS': 50,
            'NUM_FC_FILTERS': [2048, 1024],
            'HIDDEN_LIST': [256, 256],
            'PRESET': {
                'NUM_JOINTS': 17,
                'IMAGE_SIZE': [256, 192]  # [height, width]
            }
        }
    
    def _get_default_detection_config(self):
        """Get default detection configuration."""
        return {
            'pretrained': True,
            'num_classes': 2,  # background + person
            'score_threshold': 0.5,
            'nms_threshold': 0.5
        }
    
    def _get_default_tracking_config(self):
        """Get default tracking configuration."""
        return {
            'association_type': 'distribution_aware',
            'motion_model': 'kalman',
            'max_age': 30,
            'min_hits': 3,
            'bbox_weight': 0.3,
            'pose_weight': 0.7,
            'max_distance': 100.0,
            'uncertainty_threshold': 10.0
        }
    
    def forward(self, images, targets=None, mode='train'):
        """
        Forward pass of MPPET-RLE.
        
        Args:
            images: Input images tensor (B, C, H, W) or list of images
            targets: Ground truth targets for training
            mode: 'train', 'detect', or 'track'
            
        Returns:
            If training: dictionary with losses
            If detection: list of detection results with poses
            If tracking: list of tracking results
        """
        if mode == 'train' and targets is not None:
            return self._forward_train(images, targets)
        elif mode == 'detect':
            return self._forward_detect(images)
        elif mode == 'track':
            return self._forward_track(images)
        else:
            raise ValueError(f"Unknown mode: {mode}")
    
    def _forward_train(self, images, targets):
        """Training forward pass."""
        losses = {}
        
        # Detection loss
        if self.joint_training:
            detection_losses = self.detector(images, targets.get('detection_targets'))
            for key, value in detection_losses.items():
                losses[f'detection_{key}'] = value * self.detection_loss_weight
        
        # Pose estimation loss
        # Extract person crops from ground truth bounding boxes
        pose_crops, pose_targets = self._extract_pose_crops_train(images, targets)
        
        if len(pose_crops) > 0:
            # Stack crops into batch
            pose_batch = torch.stack(pose_crops)
            
            # Forward through pose estimator
            pose_output = self.pose_estimator(pose_batch, pose_targets)
            
            # Compute pose loss (this will be handled by RLELoss)
            losses['pose_output'] = pose_output
            losses['pose_targets'] = pose_targets
        
        return losses
    
    def _forward_detect(self, images):
        """Detection-only forward pass."""
        # Get detections from detector
        detections = self.detector(images)
        
        # For each detection, estimate pose
        results = []
        
        for img_idx, (image, detection) in enumerate(zip(images, detections)):
            img_results = []
            
            if len(detection['boxes']) > 0:
                # Extract crops for pose estimation
                crops = self._extract_crops_from_detections(image, detection['boxes'])
                
                if len(crops) > 0:
                    # Stack crops and run pose estimation
                    crop_batch = torch.stack(crops)
                    pose_output = self.pose_estimator(crop_batch)
                    
                    # Convert poses back to image coordinates
                    poses_img_coords = self._convert_poses_to_image_coords(
                        pose_output.pred_jts, pose_output.sigma, detection['boxes']
                    )
                    
                    # Combine detection and pose results
                    for i, (bbox, score) in enumerate(zip(detection['boxes'], detection['scores'])):
                        result = {
                            'bbox': bbox.cpu().numpy(),
                            'pose_mu': poses_img_coords[i]['mu'],
                            'pose_sigma': poses_img_coords[i]['sigma'],
                            'score': score.cpu().numpy()
                        }
                        img_results.append(result)
            
            results.append(img_results)
        
        return results
    
    def _forward_track(self, images):
        """Tracking forward pass."""
        # First get detections with poses
        detection_results = self._forward_detect(images)
        
        # Process each image through tracker
        tracking_results = []
        
        for img_detections in detection_results:
            # Convert to format expected by tracker
            tracker_detections = []
            for det in img_detections:
                tracker_det = {
                    'bbox': det['bbox'],
                    'pose_mu': det['pose_mu'],
                    'pose_sigma': det['pose_sigma'],
                    'score': det['score']
                }
                tracker_detections.append(tracker_det)
            
            # Run tracker
            tracks = self.tracker(tracker_detections)
            tracking_results.append(tracks)
        
        return tracking_results
    
    def _extract_pose_crops_train(self, images, targets):
        """Extract person crops for pose estimation during training."""
        crops = []
        pose_targets = {'target_uv': [], 'target_uv_weight': []}
        
        # This is a simplified implementation
        # In practice, you would extract crops from ground truth bounding boxes
        # and prepare pose targets in the correct format
        
        for img_idx, image in enumerate(images):
            # Get ground truth data for this image
            if 'pose_targets' in targets:
                img_targets = targets['pose_targets'][img_idx]
                
                # Extract crops and prepare targets
                # This would involve cropping the image based on GT bboxes
                # and transforming the pose coordinates to crop coordinates
                
                # Placeholder implementation
                if 'crops' in img_targets:
                    crops.extend(img_targets['crops'])
                    pose_targets['target_uv'].extend(img_targets['target_uv'])
                    pose_targets['target_uv_weight'].extend(img_targets['target_uv_weight'])
        
        # Convert to tensors
        if len(crops) > 0:
            pose_targets['target_uv'] = torch.stack(pose_targets['target_uv'])
            pose_targets['target_uv_weight'] = torch.stack(pose_targets['target_uv_weight'])
        
        return crops, pose_targets
    
    def _extract_crops_from_detections(self, image, bboxes):
        """Extract crops from detected bounding boxes."""
        crops = []
        
        for bbox in bboxes:
            # Convert bbox to integers
            x1, y1, x2, y2 = bbox.int().cpu().numpy()
            
            # Add padding
            pad = 10
            x1 = max(0, x1 - pad)
            y1 = max(0, y1 - pad)
            x2 = min(image.shape[2], x2 + pad)
            y2 = min(image.shape[1], y2 + pad)
            
            # Extract crop
            crop = image[:, y1:y2, x1:x2]
            
            # Resize to standard size
            crop_resized = F.interpolate(
                crop.unsqueeze(0), 
                size=self.crop_size[::-1],  # (height, width)
                mode='bilinear', 
                align_corners=False
            ).squeeze(0)
            
            crops.append(crop_resized)
        
        return crops
    
    def _convert_poses_to_image_coords(self, pred_poses, pred_sigmas, bboxes):
        """Convert pose coordinates from crop space to image space."""
        results = []
        
        for i, bbox in enumerate(bboxes):
            x1, y1, x2, y2 = bbox.cpu().numpy()
            
            # Scale factors
            scale_x = (x2 - x1) / self.crop_size[0]
            scale_y = (y2 - y1) / self.crop_size[1]
            
            # Convert pose coordinates
            pose_mu = pred_poses[i].cpu().numpy()  # (num_joints, 2)
            pose_sigma = pred_sigmas[i].cpu().numpy()  # (num_joints, 2)
            
            # Scale and translate
            pose_mu_img = pose_mu.copy()
            pose_mu_img[:, 0] = pose_mu[:, 0] * scale_x + x1
            pose_mu_img[:, 1] = pose_mu[:, 1] * scale_y + y1
            
            # Scale uncertainties
            pose_sigma_img = pose_sigma.copy()
            pose_sigma_img[:, 0] = pose_sigma[:, 0] * scale_x
            pose_sigma_img[:, 1] = pose_sigma[:, 1] * scale_y
            
            results.append({
                'mu': pose_mu_img,
                'sigma': pose_sigma_img
            })
        
        return results
    
    def reset_tracker(self):
        """Reset tracker state."""
        self.tracker.reset()
    
    def set_mode(self, mode):
        """Set model mode."""
        if mode == 'train':
            self.train()
        else:
            self.eval()


def build_mppet_rle(cfg):
    """Build MPPET-RLE model from configuration."""
    return MPPET_RLE(
        pose_config=cfg.get('pose_config'),
        detection_config=cfg.get('detection_config'),
        tracking_config=cfg.get('tracking_config'),
        joint_training=cfg.get('joint_training', True),
        pose_loss_weight=cfg.get('pose_loss_weight', 1.0),
        detection_loss_weight=cfg.get('detection_loss_weight', 1.0)
    )
