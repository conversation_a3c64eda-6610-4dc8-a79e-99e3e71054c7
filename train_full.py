"""
Full training script for MPPET-RLE with comprehensive monitoring
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime
import time
import json
import matplotlib.pyplot as plt

# Import our modules
from simple_rle_pose import SimpleRegress<PERSON>low
from RLE_regression_loss import R<PERSON>Loss
from utils.visualization import draw_pose_with_uncertainty, visualize_uncertainty_distribution
from utils.metrics import PoseTrackingMetrics
import cv2


def setup_logging(log_dir):
    """Setup comprehensive logging."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'full_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # Create logger
    logger = logging.getLogger('MPPET_RLE')
    logger.setLevel(logging.INFO)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    simple_formatter = logging.Formatter('%(levelname)s: %(message)s')
    
    # File handler (detailed)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler (simple)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


class TrainingMonitor:
    """Monitor training progress and metrics."""
    
    def __init__(self, log_dir):
        self.log_dir = log_dir
        self.metrics = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': [],
            'epoch_time': [],
            'pose_accuracy': [],
            'uncertainty_stats': []
        }
        self.start_time = time.time()
        
    def log_epoch(self, epoch, train_loss, val_loss, lr, epoch_time, pose_acc=None, uncertainty_stats=None):
        """Log metrics for an epoch."""
        self.metrics['train_loss'].append(train_loss)
        self.metrics['val_loss'].append(val_loss)
        self.metrics['learning_rate'].append(lr)
        self.metrics['epoch_time'].append(epoch_time)
        
        if pose_acc is not None:
            self.metrics['pose_accuracy'].append(pose_acc)
        if uncertainty_stats is not None:
            self.metrics['uncertainty_stats'].append(uncertainty_stats)
    
    def save_metrics(self):
        """Save metrics to JSON file."""
        metrics_file = os.path.join(self.log_dir, 'training_metrics.json')
        with open(metrics_file, 'w') as f:
            json.dump(self.metrics, f, indent=2)
    
    def plot_metrics(self, save_path=None):
        """Plot training metrics."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Loss curves
        epochs = range(len(self.metrics['train_loss']))
        axes[0, 0].plot(epochs, self.metrics['train_loss'], 'b-', label='Train Loss')
        if self.metrics['val_loss']:
            axes[0, 0].plot(epochs, self.metrics['val_loss'], 'r-', label='Val Loss')
        axes[0, 0].set_title('Training Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Learning rate
        axes[0, 1].plot(epochs, self.metrics['learning_rate'], 'g-')
        axes[0, 1].set_title('Learning Rate')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('LR')
        axes[0, 1].grid(True)
        
        # Epoch time
        axes[1, 0].plot(epochs, self.metrics['epoch_time'], 'm-')
        axes[1, 0].set_title('Epoch Time')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Time (s)')
        axes[1, 0].grid(True)
        
        # Pose accuracy (if available)
        if self.metrics['pose_accuracy']:
            axes[1, 1].plot(epochs[:len(self.metrics['pose_accuracy'])], self.metrics['pose_accuracy'], 'c-')
            axes[1, 1].set_title('Pose Accuracy')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Accuracy')
            axes[1, 1].grid(True)
        else:
            axes[1, 1].text(0.5, 0.5, 'Pose Accuracy\n(Not Available)', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
        else:
            plt.show()


def create_enhanced_dataset(num_samples=1000, batch_size=8, difficulty='medium'):
    """Create enhanced dummy dataset with varying difficulty."""
    
    class EnhancedDataset:
        def __init__(self, num_samples, difficulty):
            self.num_samples = num_samples
            self.difficulty = difficulty
            
        def __len__(self):
            return self.num_samples
        
        def __getitem__(self, idx):
            # Create more realistic dummy data based on difficulty
            if self.difficulty == 'easy':
                noise_level = 0.1
                pose_variation = 20
            elif self.difficulty == 'medium':
                noise_level = 0.3
                pose_variation = 50
            else:  # hard
                noise_level = 0.5
                pose_variation = 80
            
            # Create dummy image with some structure
            image = torch.randn(3, 256, 192) * noise_level + 0.5
            
            # Create more realistic pose (human-like proportions)
            # Head
            head_center = torch.tensor([128, 50])
            # Torso
            torso_center = torch.tensor([128, 120])
            # Limbs
            left_arm = torch.tensor([100, 100])
            right_arm = torch.tensor([156, 100])
            left_leg = torch.tensor([110, 180])
            right_leg = torch.tensor([146, 180])
            
            # Create full pose with variations
            pose = torch.zeros(17, 2)
            pose[0] = head_center + torch.randn(2) * pose_variation * 0.3  # nose
            pose[1] = head_center + torch.randn(2) * pose_variation * 0.2  # head_bottom
            pose[2] = head_center + torch.randn(2) * pose_variation * 0.2  # head_top
            pose[3] = head_center + torch.tensor([-15, 0]) + torch.randn(2) * pose_variation * 0.2  # left_ear
            pose[4] = head_center + torch.tensor([15, 0]) + torch.randn(2) * pose_variation * 0.2   # right_ear
            
            # Shoulders and arms
            pose[5] = left_arm + torch.randn(2) * pose_variation * 0.3   # left_shoulder
            pose[6] = right_arm + torch.randn(2) * pose_variation * 0.3  # right_shoulder
            pose[7] = left_arm + torch.tensor([0, 30]) + torch.randn(2) * pose_variation * 0.4   # left_elbow
            pose[8] = right_arm + torch.tensor([0, 30]) + torch.randn(2) * pose_variation * 0.4  # right_elbow
            pose[9] = left_arm + torch.tensor([0, 60]) + torch.randn(2) * pose_variation * 0.5   # left_wrist
            pose[10] = right_arm + torch.tensor([0, 60]) + torch.randn(2) * pose_variation * 0.5 # right_wrist
            
            # Hips and legs
            pose[11] = left_leg + torch.tensor([0, -30]) + torch.randn(2) * pose_variation * 0.3  # left_hip
            pose[12] = right_leg + torch.tensor([0, -30]) + torch.randn(2) * pose_variation * 0.3 # right_hip
            pose[13] = left_leg + torch.randn(2) * pose_variation * 0.4   # left_knee
            pose[14] = right_leg + torch.randn(2) * pose_variation * 0.4  # right_knee
            pose[15] = left_leg + torch.tensor([0, 30]) + torch.randn(2) * pose_variation * 0.5  # left_ankle
            pose[16] = right_leg + torch.tensor([0, 30]) + torch.randn(2) * pose_variation * 0.5 # right_ankle
            
            # Ensure poses are within image bounds
            pose[:, 0] = torch.clamp(pose[:, 0], 10, 182)  # width
            pose[:, 1] = torch.clamp(pose[:, 1], 10, 246)  # height
            
            # Create visibility weights (some joints might be occluded)
            weights = torch.ones(17, 2)
            if torch.rand(1) < 0.2:  # 20% chance of occlusion
                occluded_joints = torch.randperm(17)[:torch.randint(1, 4, (1,))]
                weights[occluded_joints] = 0.1  # Low weight for occluded joints
            
            return {
                'image': image,
                'target_uv': pose,
                'target_uv_weight': weights
            }
    
    dataset = EnhancedDataset(num_samples, difficulty)
    
    def collate_fn(batch):
        images = torch.stack([item['image'] for item in batch])
        target_uv = torch.stack([item['target_uv'] for item in batch])
        target_uv_weight = torch.stack([item['target_uv_weight'] for item in batch])
        
        return {
            'images': images,
            'targets': {
                'target_uv': target_uv,
                'target_uv_weight': target_uv_weight
            }
        }
    
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=2,
        pin_memory=True
    )
    
    return dataloader


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch, monitor):
    """Enhanced training epoch with detailed monitoring."""
    model.train()
    
    total_loss = 0.0
    total_coord_loss = 0.0
    total_sigma_loss = 0.0
    num_batches = 0
    
    # Metrics for pose accuracy
    total_pose_error = 0.0
    total_uncertainty = 0.0
    
    epoch_start = time.time()
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}', leave=False)
    
    for batch_idx, batch in enumerate(pbar):
        batch_start = time.time()
        
        images = batch['images'].to(device)
        targets = {
            'target_uv': batch['targets']['target_uv'].to(device),
            'target_uv_weight': batch['targets']['target_uv_weight'].to(device)
        }
        
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(images, targets)
        
        # Compute loss
        loss = criterion(outputs, targets)
        
        # Compute additional metrics
        with torch.no_grad():
            # Pose error (L2 distance)
            pose_error = torch.mean(torch.norm(outputs.pred_jts - targets['target_uv'], dim=2))
            total_pose_error += pose_error.item()
            
            # Average uncertainty
            avg_uncertainty = torch.mean(outputs.sigma)
            total_uncertainty += avg_uncertainty.item()
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
        
        batch_time = time.time() - batch_start
        
        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item():.2f}',
            'PoseErr': f'{pose_error.item():.2f}',
            'Sigma': f'{avg_uncertainty.item():.3f}',
            'GradNorm': f'{grad_norm:.3f}',
            'BatchTime': f'{batch_time:.2f}s'
        })
        
        # Log detailed metrics every 50 batches
        if batch_idx % 50 == 0:
            logger.info(f'Epoch {epoch}, Batch {batch_idx}: '
                       f'Loss={loss.item():.4f}, '
                       f'PoseError={pose_error.item():.4f}, '
                       f'AvgSigma={avg_uncertainty.item():.4f}')
    
    epoch_time = time.time() - epoch_start
    avg_loss = total_loss / max(num_batches, 1)
    avg_pose_error = total_pose_error / max(num_batches, 1)
    avg_uncertainty = total_uncertainty / max(num_batches, 1)
    
    logger.info(f'Epoch {epoch} Training: '
               f'AvgLoss={avg_loss:.4f}, '
               f'AvgPoseError={avg_pose_error:.4f}, '
               f'AvgUncertainty={avg_uncertainty:.4f}, '
               f'Time={epoch_time:.2f}s')
    
    return avg_loss, avg_pose_error, avg_uncertainty, epoch_time


def validate_epoch(model, dataloader, criterion, device, logger, epoch, vis_dir=None):
    """Enhanced validation with comprehensive metrics."""
    model.eval()
    
    total_loss = 0.0
    total_pose_error = 0.0
    total_uncertainty = 0.0
    num_batches = 0
    
    all_uncertainties = []
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation {epoch}', leave=False)
        
        for batch_idx, batch in enumerate(pbar):
            images = batch['images'].to(device)
            targets = {
                'target_uv': batch['targets']['target_uv'].to(device),
                'target_uv_weight': batch['targets']['target_uv_weight'].to(device)
            }
            
            # Forward pass
            outputs = model(images)
            
            # Compute loss (without gradients)
            loss = criterion(outputs, targets)
            
            # Compute metrics
            pose_error = torch.mean(torch.norm(outputs.pred_jts - targets['target_uv'], dim=2))
            avg_uncertainty = torch.mean(outputs.sigma)
            
            total_loss += loss.item()
            total_pose_error += pose_error.item()
            total_uncertainty += avg_uncertainty.item()
            num_batches += 1
            
            # Collect uncertainties for analysis
            all_uncertainties.append(outputs.sigma.cpu().numpy())
            
            # Save visualizations for first batch
            if vis_dir and batch_idx == 0:
                for i in range(min(len(images), 3)):
                    img_np = images[i].cpu().permute(1, 2, 0).numpy()
                    img_np = ((img_np + 1) * 127.5).clip(0, 255).astype(np.uint8)
                    
                    pred_pose = outputs.pred_jts[i].cpu().numpy()
                    pred_sigma = outputs.sigma[i].cpu().numpy()
                    gt_pose = targets['target_uv'][i].cpu().numpy()
                    
                    # Create visualization
                    vis_img = draw_pose_with_uncertainty(img_np, pred_pose, pred_sigma, color=(0, 255, 0))
                    
                    # Draw ground truth
                    for j, point in enumerate(gt_pose):
                        x, y = int(point[0]), int(point[1])
                        if 0 <= x < img_np.shape[1] and 0 <= y < img_np.shape[0]:
                            cv2.circle(vis_img, (x, y), 3, (255, 0, 0), -1)
                    
                    # Save
                    save_path = os.path.join(vis_dir, f'val_epoch_{epoch}_img_{i}.jpg')
                    cv2.imwrite(save_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.2f}',
                'PoseErr': f'{pose_error.item():.2f}',
                'Sigma': f'{avg_uncertainty.item():.3f}'
            })
    
    avg_loss = total_loss / max(num_batches, 1)
    avg_pose_error = total_pose_error / max(num_batches, 1)
    avg_uncertainty = total_uncertainty / max(num_batches, 1)
    
    # Analyze uncertainty distribution
    if all_uncertainties:
        uncertainties = np.concatenate(all_uncertainties, axis=0)
        uncertainty_stats = {
            'mean': float(np.mean(uncertainties)),
            'std': float(np.std(uncertainties)),
            'min': float(np.min(uncertainties)),
            'max': float(np.max(uncertainties))
        }
        
        # Save uncertainty distribution plot
        if vis_dir:
            uncertainty_plot_path = os.path.join(vis_dir, f'uncertainty_dist_epoch_{epoch}.png')
            avg_uncertainty_per_joint = np.mean(uncertainties, axis=(0, 2))
            visualize_uncertainty_distribution(
                avg_uncertainty_per_joint.reshape(-1, 1), 
                save_path=uncertainty_plot_path
            )
    else:
        uncertainty_stats = None
    
    logger.info(f'Epoch {epoch} Validation: '
               f'AvgLoss={avg_loss:.4f}, '
               f'AvgPoseError={avg_pose_error:.4f}, '
               f'AvgUncertainty={avg_uncertainty:.4f}')
    
    return avg_loss, avg_pose_error, avg_uncertainty, uncertainty_stats


def main():
    parser = argparse.ArgumentParser(description='Full MPPET-RLE Training')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints_full', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs_full', help='Log directory')
    parser.add_argument('--vis_dir', type=str, default='visualizations_full', help='Visualization directory')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--num_samples', type=int, default=2000, help='Number of training samples')
    parser.add_argument('--difficulty', type=str, default='medium', choices=['easy', 'medium', 'hard'],
                       help='Dataset difficulty level')
    parser.add_argument('--resume', type=str, default=None, help='Resume from checkpoint')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.vis_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info("="*60)
    logger.info("MPPET-RLE FULL TRAINING STARTED")
    logger.info("="*60)
    logger.info(f'Training configuration: {vars(args)}')
    
    # Setup monitoring
    monitor = TrainingMonitor(args.log_dir)
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Create datasets
    logger.info(f'Creating datasets with {args.num_samples} samples, difficulty: {args.difficulty}')
    train_loader = create_enhanced_dataset(args.num_samples, args.batch_size, args.difficulty)
    val_loader = create_enhanced_dataset(args.num_samples // 4, args.batch_size, args.difficulty)
    
    # Model
    model = SimpleRegressFlow(num_joints=17)
    model = model.to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f'Model: {total_params:,} total parameters, {trainable_params:,} trainable')
    
    # Loss function
    criterion = RLELoss()
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs, eta_min=1e-6)
    
    # Resume from checkpoint
    start_epoch = 0
    best_loss = float('inf')
    
    if args.resume:
        if os.path.isfile(args.resume):
            logger.info(f'Resuming from checkpoint: {args.resume}')
            checkpoint = torch.load(args.resume, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            scheduler.load_state_dict(checkpoint.get('scheduler_state_dict', {}))
            start_epoch = checkpoint['epoch'] + 1
            best_loss = checkpoint.get('best_loss', float('inf'))
            logger.info(f'Resumed from epoch {start_epoch}, best loss: {best_loss:.4f}')
        else:
            logger.warning(f'Checkpoint not found: {args.resume}')
    
    # Training loop
    logger.info("Starting training loop...")
    
    for epoch in range(start_epoch, args.epochs):
        epoch_start_time = time.time()
        
        logger.info(f'\n{"="*20} EPOCH {epoch+1}/{args.epochs} {"="*20}')
        
        # Train
        train_loss, train_pose_error, train_uncertainty, train_time = train_epoch(
            model, train_loader, optimizer, criterion, device, logger, epoch, monitor
        )
        
        # Validate
        val_loss, val_pose_error, val_uncertainty, uncertainty_stats = validate_epoch(
            model, val_loader, criterion, device, logger, epoch, 
            vis_dir=args.vis_dir if epoch % 10 == 0 else None
        )
        
        # Update learning rate
        current_lr = optimizer.param_groups[0]['lr']
        scheduler.step()
        new_lr = optimizer.param_groups[0]['lr']
        
        # Calculate pose accuracy (inverse of error)
        pose_accuracy = 1.0 / (1.0 + val_pose_error)
        
        # Log to monitor
        monitor.log_epoch(epoch, train_loss, val_loss, current_lr, train_time, 
                         pose_accuracy, uncertainty_stats)
        
        # Save metrics and plots
        monitor.save_metrics()
        if epoch % 10 == 0:
            plot_path = os.path.join(args.vis_dir, f'training_curves_epoch_{epoch}.png')
            monitor.plot_metrics(plot_path)
        
        # Save checkpoints
        is_best = val_loss < best_loss
        if is_best:
            best_loss = val_loss
            
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'train_loss': train_loss,
            'val_loss': val_loss,
            'best_loss': best_loss,
            'args': vars(args)
        }
        
        # Save best model
        if is_best:
            best_path = os.path.join(args.save_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            logger.info(f'✅ NEW BEST MODEL! Val Loss: {val_loss:.4f} (prev: {best_loss:.4f})')
        
        # Save regular checkpoint
        if epoch % 10 == 0:
            checkpoint_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, checkpoint_path)
        
        # Save latest checkpoint
        latest_path = os.path.join(args.save_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, latest_path)
        
        epoch_total_time = time.time() - epoch_start_time
        
        # Print epoch summary
        logger.info(f'Epoch {epoch+1} Summary:')
        logger.info(f'  Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}')
        logger.info(f'  Train Pose Error: {train_pose_error:.4f} | Val Pose Error: {val_pose_error:.4f}')
        logger.info(f'  Train Uncertainty: {train_uncertainty:.4f} | Val Uncertainty: {val_uncertainty:.4f}')
        logger.info(f'  Learning Rate: {current_lr:.2e} → {new_lr:.2e}')
        logger.info(f'  Epoch Time: {epoch_total_time:.2f}s')
        logger.info(f'  Best Val Loss: {best_loss:.4f} {"(NEW!)" if is_best else ""}')
        
        # Early stopping check (optional)
        if epoch > 20 and val_loss > best_loss * 1.5:
            logger.warning(f'Validation loss diverging. Consider stopping.')
    
    # Final summary
    total_time = time.time() - monitor.start_time
    logger.info("\n" + "="*60)
    logger.info("TRAINING COMPLETED!")
    logger.info("="*60)
    logger.info(f'Total training time: {total_time/3600:.2f} hours')
    logger.info(f'Best validation loss: {best_loss:.4f}')
    logger.info(f'Final learning rate: {optimizer.param_groups[0]["lr"]:.2e}')
    
    # Save final plots
    final_plot_path = os.path.join(args.vis_dir, 'final_training_curves.png')
    monitor.plot_metrics(final_plot_path)
    logger.info(f'Training curves saved to: {final_plot_path}')
    
    # Save final metrics
    monitor.save_metrics()
    logger.info(f'Training metrics saved to: {os.path.join(args.log_dir, "training_metrics.json")}')


if __name__ == '__main__':
    main()
